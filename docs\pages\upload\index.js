import Toast from 'tdesign-miniprogram/toast/index';
import Message from 'tdesign-miniprogram/message/index';
import request from '~/api/request';

Page({
  data: {
    uploadFiles: [],
    artworkTitle: '',
    uploading: false,
    uploadProgress: [],
    canUpload: false,
    gridConfig: {
      column: 2,
      width: 340,
      height: 340,
    },
  },

  onLoad() {
    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再上传作品',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login',
            });
          } else {
            wx.navigateBack();
          }
        }
      });
    }

    // 初始化canUpload状态
    this.updateCanUpload();
  },

  // 更新是否可以上传的状态
  updateCanUpload() {
    const canUpload = this.data.uploadFiles.length > 0 &&
                     this.data.artworkTitle.trim() &&
                     !this.data.uploading;
    this.setData({ canUpload });
  },

  // 处理上传成功
  handleUploadSuccess(e) {
    const { files } = e.detail;
    this.setData({
      uploadFiles: files
    });
    this.updateCanUpload();
  },

  // 处理移除文件
  handleRemove(e) {
    const { files } = e.detail;
    this.setData({
      uploadFiles: files
    });
    this.updateCanUpload();
  },

  // 处理上传失败
  handleUploadFail(e) {
    console.error('上传失败:', e.detail);
    Toast({
      context: this,
      selector: '#t-toast',
      message: '图片上传失败，请重试',
      theme: 'error',
    });
  },

  // 标题输入变化
  onTitleChange(e) {
    this.setData({
      artworkTitle: e.detail.value
    });
    this.updateCanUpload();
  },

  // 提交上传
  async submitUpload() {
    if (!this.data.canUpload) return;

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      Toast({
        context: this,
        selector: '#t-toast',
        message: '请先登录',
        theme: 'error',
      });
      return;
    }

    this.setData({ uploading: true });
    this.updateCanUpload();
    this.updateCanUpload();

    try {
      // 初始化进度列表
      const progressList = this.data.uploadFiles.map((file, index) => ({
        id: index,
        name: `图片${index + 1}`,
        progress: 0,
        status: '准备上传...'
      }));
      
      this.setData({ uploadProgress: progressList });

      // 上传图片文件
      const uploadedUrls = [];
      for (let i = 0; i < this.data.uploadFiles.length; i++) {
        const file = this.data.uploadFiles[i];
        
        // 更新进度
        this.updateProgress(i, 10, '上传中...');
        
        try {
          // 这里需要实现实际的文件上传逻辑
          // 由于小程序的限制，我们需要使用wx.uploadFile
          const uploadResult = await this.uploadSingleFile(file.url, i);
          uploadedUrls.push(uploadResult);
          
          this.updateProgress(i, 100, '上传完成');
        } catch (error) {
          this.updateProgress(i, 0, '上传失败');
          throw error;
        }
      }

      // 创建作品记录
      const artworkData = {
        userId: userInfo.id,
        title: this.data.artworkTitle,
        images: uploadedUrls
      };

      const result = await request('/artwork', 'POST', artworkData);
      
      Toast({
        context: this,
        selector: '#t-toast',
        message: '上传成功！AI正在评价中...',
        theme: 'success',
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack({
          success: () => {
            // 通知首页刷新数据
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage && prevPage.route === 'pages/home/<USER>') {
              prevPage.loadData && prevPage.loadData();
            }
          }
        });
      }, 1500);

    } catch (error) {
      console.error('上传失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.msg || '上传失败，请重试',
        theme: 'error',
      });
    } finally {
      this.setData({ uploading: false });
      this.updateCanUpload();
    }
  },

  // 上传单个文件
  uploadSingleFile(filePath, index) {
    return new Promise((resolve, reject) => {
      const baseUrl = wx.getStorageSync('baseUrl') || 'http://localhost:8080/api';
      wx.uploadFile({
        url: baseUrl + '/upload/image',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 0) {
              resolve(data.data);
            } else {
              reject(new Error(data.msg || '上传失败'));
            }
          } catch (e) {
            reject(new Error('响应解析失败'));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 更新上传进度
  updateProgress(index, progress, status) {
    const progressList = [...this.data.uploadProgress];
    if (progressList[index]) {
      progressList[index].progress = progress;
      progressList[index].status = status;
      this.setData({ uploadProgress: progressList });
    }
  }
});
