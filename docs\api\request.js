import config from '~/config';

const { baseUrl } = config;
const delay = config.isMock ? 500 : 0;

// 获取app实例
const getAppInstance = () => {
  try {
    return getApp();
  } catch (e) {
    return null;
  }
};
function request(url, method = 'GET', data = {}) {
  const header = {
    'content-type': 'application/json',
    // 有其他content-type需求加点逻辑判断处理即可
  };
  // 获取token，有就丢进请求头
  const tokenString = wx.getStorageSync('access_token');
  if (tokenString) {
    header.Authorization = `Bearer ${tokenString}`;
  }
  return new Promise((resolve, reject) => {
    wx.request({
      url: baseUrl + url,
      method,
      data,
      dataType: 'json', // 微信官方文档中介绍会对数据进行一次JSON.parse
      header,
      success(res) {
        setTimeout(() => {
          // HTTP状态码为200才视为成功
          if (res.statusCode === 200) {
            // 检查业务状态码
            if (res.data && res.data.code === 0) {
              resolve(res.data);
            } else {
              // 检查是否是token过期或无效
              if (res.data && (res.data.code === 401 || res.data.code === 403)) {
                console.log('Token过期或无效，清除登录状态');
                const app = getAppInstance();
                if (app && app.logout) {
                  app.logout();
                  return;
                }
              }
              // 业务错误
              reject(res.data || res);
            }
          } else if (res.statusCode === 401 || res.statusCode === 403) {
            // HTTP 401/403 也表示认证失败
            console.log('HTTP认证失败，清除登录状态');
            const app = getAppInstance();
            if (app && app.logout) {
              app.logout();
              return;
            }
            reject(res);
          } else {
            // 其他HTTP错误
            reject(res);
          }
        }, delay);
      },
      fail(err) {
        setTimeout(() => {
          // 断网、服务器挂了都会fail回调，直接reject即可
          reject(err);
        }, delay);
      },
    });
  });
}

// 导出请求和服务地址
export default request;
