import Message from 'tdesign-miniprogram/message/index';
import request from '~/api/request';

// 获取应用实例
const app = getApp();

Page({
  data: {
    enable: false,
    cardInfo: [],
    uploadCardInfo: [],
    currentUserId: null,
    activeTab: 'recommend', // 当前激活的tab
    currentSearchData: {}, // 当前搜索条件
  },
  // 生命周期
  async onReady() {
    await this.loadData();
  },

  async loadData() {
    try {
      // 获取推荐作品列表
      const homeRes = await request('/artwork/home', 'GET');

      // 获取当前用户ID（从本地存储获取）
      const userInfo = wx.getStorageSync('userInfo');
      const currentUserId = userInfo ? userInfo.id : null;

      // 检查响应是否成功
      if (homeRes && (homeRes.code === 0 || homeRes.code === 200)) {
        this.setData({
          cardInfo: homeRes.data.records || [],
          currentUserId: currentUserId
        });

        // 如果用户已登录，获取用户上传的作品
        if (currentUserId) {
          await this.loadUserUploads(currentUserId);
        }
      } else {
        console.error('获取首页数据失败:', homeRes);
        wx.showToast({
          title: homeRes?.msg || homeRes?.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  async loadUserUploads(userId) {
    try {
      const userRes = await request(`/artwork/user/${userId}`, 'GET');
      if (userRes && (userRes.code === 0 || userRes.code === 200)) {
        this.setData({
          uploadCardInfo: userRes.data.records || []
        });
      } else {
        console.error('获取用户作品失败:', userRes);
      }
    } catch (error) {
      console.error('加载用户作品失败:', error);
    }
  },
  onLoad(option) {
    if (option.oper) {
      let content = '';
      if (option.oper === 'upload') {
        content = '上传成功';
      } else if (option.oper === 'save') {
        content = '保存成功';
      }
      this.showOperMsg(content);
    }
  },

  onShow() {
    // 检查登录状态
    app.checkLoginStatus();

    // 页面显示时重新加载数据，以便显示最新的上传作品
    this.loadData();
  },
  onRefresh() {
    this.refresh();
  },

  async refresh() {
    this.setData({
      enable: true,
    });

    await this.loadData();

    setTimeout(() => {
      this.setData({
        enable: false,
      });
    }, 1500);
  },
  showOperMsg(content) {
    Message.success({
      context: this,
      offset: [120, 32],
      duration: 4000,
      content,
    });
  },

  // 查看作品详情
  viewArtworkDetail(e) {
    const artworkId = e.currentTarget.dataset.id;
    if (artworkId) {
      wx.navigateTo({
        url: `/pages/artwork-detail/index?id=${artworkId}`,
      });
    }
  },

  // Tab切换处理
  onTabChange(e) {
    const activeTab = e.detail.value;
    this.setData({
      activeTab: activeTab,
    });

    // 切换tab时重新加载对应数据
    if (activeTab === 'recommend') {
      this.loadDataWithSearch(this.data.currentSearchData);
    } else if (activeTab === 'upload') {
      this.loadUserUploadsWithSearch(this.data.currentUserId, this.data.currentSearchData);
    }
  },

  // 处理搜索条件变化
  onSearchChange(e) {
    const searchData = e.detail;
    console.log('搜索条件:', searchData);

    this.setData({
      currentSearchData: searchData,
    });

    // 根据当前tab和搜索条件加载数据
    if (searchData.tab === 'recommend') {
      this.loadDataWithSearch(searchData);
    } else if (searchData.tab === 'upload') {
      // 如果要显示所有推荐数据，使用下面这行
      this.loadDataWithSearch(searchData);
      // 如果要显示用户上传的作品，使用下面这行（注释掉上面一行）
      // this.loadUserUploadsWithSearch(this.data.currentUserId, searchData);
    }
  },

  // 根据搜索条件加载推荐数据
  async loadDataWithSearch(searchData) {
    try {
      // 构建查询参数
      const params = {};
      if (searchData.keyword) params.keyword = searchData.keyword;
      if (searchData.type) params.type = searchData.type;
      if (searchData.status) params.status = searchData.status;
      if (searchData.time) params.time = searchData.time;

      // 调用API获取搜索后的数据
      const homeRes = await request('/artwork/home', 'GET', params);

      this.setData({
        cardInfo: homeRes.data.records || [],
      });

      if (searchData.keyword || searchData.type || searchData.status || searchData.time) {
        wx.showToast({
          title: '搜索完成',
          icon: 'success',
          duration: 1500
        });
      }
    } catch (error) {
      console.error('搜索数据失败:', error);
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  // 根据搜索条件加载用户上传数据
  async loadUserUploadsWithSearch(userId, searchData) {
    if (!userId) return;

    try {
      // 构建查询参数
      const params = {};
      if (searchData.keyword) params.keyword = searchData.keyword;
      if (searchData.status) params.status = searchData.status;
      if (searchData.time) params.time = searchData.time;

      // 调用API获取用户作品
      const userRes = await request(`/artwork/user/${userId}`, 'GET', params);

      this.setData({
        uploadCardInfo: userRes.data.records || [],
      });

      if (searchData.keyword || searchData.status || searchData.time) {
        wx.showToast({
          title: '搜索完成',
          icon: 'success',
          duration: 1500
        });
      }
    } catch (error) {
      console.error('搜索用户作品失败:', error);
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  // 跳转到上传页面
  goUpload() {
    // 使用全局登录状态检查
    if (!app.requireLogin()) {
      return;
    }

    wx.navigateTo({
      url: '/pages/upload/index',
    });
  },
});
