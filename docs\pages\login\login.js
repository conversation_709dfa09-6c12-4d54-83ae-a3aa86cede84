import Toast from 'tdesign-miniprogram/toast/index';
import Message from 'tdesign-miniprogram/message/index';
import request from '~/api/request';

const app = getApp();

Page({
  data: {
    logging: false,
  },

  // 微信一键登录
  async wxLogin() {
    this.setData({ logging: true });

    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });

      if (!loginRes.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用后端微信登录接口
      const result = await request(`/auth/wx-login?code=${loginRes.code}`, 'GET');

      if (result.code === 0) {
        // 使用全局登录状态管理
        app.setLoginStatus(result.data.userInfo, result.data.token);

        Toast({
          context: this,
          selector: '#t-toast',
          message: '登录成功',
          theme: 'success',
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>',
          });
        }, 1500);
      } else {
        throw new Error(result.msg || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      Toast({
        context: this,
        selector: '#t-toast',
        message: error.message || '登录失败，请重试',
        theme: 'error',
      });
    } finally {
      this.setData({ logging: false });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '美院作品评价系统',
      path: '/pages/home/<USER>'
    };
  }
});
