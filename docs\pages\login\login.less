page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.9;
    z-index: -1;
  }

/* 装饰圆圈 */
.login-container::after {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 400rpx;
  height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: -1;
}

.login-header {
  text-align: center;
  padding: 120rpx 60rpx 80rpx;
  color: white;
}

.login-header .logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 auto 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-header .app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.login-header .app-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 300;
}

.login-content {
  flex: 1;
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  padding: 60rpx 40rpx 40rpx;
  margin-top: auto;
  position: relative;
}

/* 顶部装饰线 */
.login-content::before {
    content: '';
    position: absolute;
    top: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 80rpx;
    height: 8rpx;
    background: #e5e5e5;
    border-radius: 4rpx;
  }
}

.feature-list {
  margin-bottom: 80rpx;
}

.feature-list .feature-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #333;
  /* 添加动画效果 */
  opacity: 0;
  transform: translateX(-20rpx);
  animation: slideInLeft 0.6s ease forwards;
}

.feature-list .feature-item .t-icon {
  margin-right: 24rpx;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
}

.feature-list .feature-item text {
  flex: 1;
  font-weight: 400;
}

/* 动画延迟 */
.feature-list .feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-list .feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-list .feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-list .feature-item:nth-child(4) { animation-delay: 0.4s; }

.login-buttons .t-button {
  --td-button-default-height: 96rpx;
  --td-button-large-height: 96rpx;
  --td-button-primary-bg-color: #667eea !important;
  --td-button-primary-border-color: transparent !important;
  --td-button-primary-color: white !important;

  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  margin-bottom: 40rpx;
  /* 按钮动画 */
  transition: all 0.3s ease;
}

.login-buttons .t-button .t-icon {
  margin-right: 16rpx;
  color: white !important;
}

/* 确保按钮内容为白色 */
.login-buttons .t-button .t-button__content {
  color: white !important;
}

/* 按钮激活状态 */
.login-buttons .t-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 微信登录按钮特定样式 */
.wx-login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
}

/* TDesign按钮内部结构样式覆盖 */
.wx-login-btn .t-button__content,
.wx-login-btn .t-button__text {
  color: white !important;
}

/* 覆盖所有可能的文本颜色 */
.wx-login-btn * {
  color: white !important;
}

/* 确保图标和文字都是白色 */
.wx-login-btn .t-icon,
.wx-login-btn text,
.wx-login-btn view {
  color: white !important;
}

/* 悬停和激活状态 */
.wx-login-btn:hover,
.wx-login-btn:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  color: white !important;
}

.login-tips {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.login-tips .link {
  color: #667eea;
  text-decoration: underline;
}

/* 动画定义 */
@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Toast 组件样式覆盖 */
.t-toast {
  --td-toast-bg-color: rgba(0, 0, 0, 0.8);
  --td-toast-text-color: white;
}

/* 响应式设计 */
@media screen and (max-height: 1200rpx) {
  .login-header {
    padding: 80rpx 60rpx 60rpx;
  }

  .login-header .logo {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
  }

  .login-header .app-name {
    font-size: 42rpx;
  }

  .login-header .app-desc {
    font-size: 26rpx;
  }

  .feature-list {
    margin-bottom: 60rpx;
  }

  .feature-list .feature-item {
    padding: 20rpx 0;
    font-size: 26rpx;
  }
}

/* 导航栏样式覆盖 */
.t-navbar {
  --td-navbar-bg-color: transparent;
  --td-navbar-color: white;
}

.t-navbar .t-navbar__left .t-icon {
  color: white !important;
}

.t-navbar .t-navbar__title {
  color: white !important;
  font-weight: 500;
}
